{"presets": ["@babel/preset-react", ["@babel/preset-env", {"modules": false, "debug": false, "corejs": 3, "useBuiltIns": "usage", "targets": {"browsers": ["> 1%", "last 2 versions", "not ie <= 8"]}}]], "plugins": [["@babel/plugin-transform-runtime", {"corejs": false, "regenerator": true}], "@babel/plugin-syntax-dynamic-import", "@babel/plugin-proposal-class-properties", "@babel/plugin-proposal-nullish-coalescing-operator", "@babel/plugin-proposal-optional-chaining"]}