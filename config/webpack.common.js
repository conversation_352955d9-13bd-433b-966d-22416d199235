const paths = require('./paths');
const webpack = require('webpack');
const ESLintPlugin = require('eslint-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const { getThemeVariables } = require('antd/dist/theme');
const getLessVariables = require('../src/utils/theme');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const ModuleScopePlugin = require('react-dev-utils/ModuleScopePlugin');
const ModuleNotFoundPlugin = require('react-dev-utils/ModuleNotFoundPlugin');
// const HtmlWebpackExternalsPlugin = require('html-webpack-externals-plugin');
const reactRefreshOverlayEntry = require.resolve('react-dev-utils/refreshOverlayInterop');
const CopyWebpackPlugin = require('copy-webpack-plugin')
const HtmlWebpackInjectPlugin = require('@didi/html-webpack-inject-plugin');
const skeleton = require('../src/components/SkeletonPage/totalSkeleton');

const isDev = process.env.NODE_ENV === 'development';
const isProd = process.env.NODE_ENV === 'production';
const shouldUseSourceMap = process.env.GENERATE_SOURCEMAP !== 'false';

const definedEnv = {
  'process.env': {
    APP_ENV: `"${process.env.APP_ENV}"`,
    // 区分本地环境和生产环境
    NODE_ENV: `"${process.env.NODE_ENV}"`,
    PUBLIC_URL: '" "',
    FAST_REFRESH: isDev ? 'true' : 'false',
  },
};

module.exports = {
  experiments: {
    asyncWebAssembly: true,
  },
  output: {
    // futureEmitAssets 在 webpack 5 中已默认启用，移除此配置
    globalObject: 'this',
  },
  resolve: {
    alias: {
      '@': paths.appSrc,
      '@/': 'src/',
      'react/jsx-runtime': require.resolve('react/jsx-runtime.js'),
      'react/jsx-dev-runtime': require.resolve('react/jsx-dev-runtime.js'),
    },
    modules: [paths.appNodeModules],
    extensions: ['.web.js', '.mjs', '.js', '.json', '.web.jsx', '.jsx'],
    plugins: [new ModuleScopePlugin(paths.appSrc, [paths.appPackageJson, reactRefreshOverlayEntry])],
    fallback: {
      util: require.resolve('util/'),
      crypto: require.resolve('crypto-browserify'),
      stream: require.resolve('stream-browserify'),
      buffer: require.resolve('buffer'),
      process: require.resolve('process/browser'),
      '@didi/halo-client-web/halo_client_web_bg': false,
    },
  },
  module: {
    strictExportPresence: true,
    rules: [
      // requireEnsure 在 webpack 5 中已移除
      {
        oneOf: [
          {
            test: /\.(xml)$/,
            loader: require.resolve('raw-loader'),
          },
          {
            test: /\.(?:png|jpe?g|gif|svg|woff|woff2|eot|ttf)\??.*$/,
            type: 'asset',
            parser: {
              dataUrlCondition: {
                maxSize: 10240, // 10kb
              },
            },
            generator: {
              filename: 'static/img/[name].[hash:8][ext]',
            },
          },
          {
            test: /\.(js|jsx)$/,
            include: paths.appSrc,
            loader: require.resolve('babel-loader'),
            options: {
              plugins: [isDev && require.resolve('react-refresh/babel')].filter(Boolean),
              cacheDirectory: true,
              cacheCompression: false,
              // compact:  isProd,
              compact: false,
              sourceMaps: shouldUseSourceMap,
              inputSourceMap: shouldUseSourceMap,
            },
          },
          {
            test: /\.(js)$/,
            exclude: /@babel(?:\/|\\{1,2})runtime/,
            loader: require.resolve('babel-loader'),
            options: {
              babelrc: false,
              configFile: false,
              compact: false,
              cacheDirectory: true,
              cacheCompression: false,
              sourceMaps: shouldUseSourceMap,
              inputSourceMap: shouldUseSourceMap,
            },
          },
          {
            test: /\.css$/,
            sideEffects: true,
            use: [isDev ? 'style-loader' : MiniCssExtractPlugin.loader, 'css-loader'],
          },
          {
            test: /\.less$/,
            sideEffects: true,
            use: [
              isDev ? 'style-loader' : MiniCssExtractPlugin.loader,
              {
                loader: 'css-loader',
                options: {
                  sourceMap: shouldUseSourceMap,
                  // 启用 css module
                  modules: {
                    auto: true,
                    localIdentName: '[local]--[hash:base64:5]',
                  },
                },
              },
              {
                loader: 'postcss-loader',
                options: {
                  sourceMap: shouldUseSourceMap,
                  postcssOptions: {
                    plugins: [
                      require('autoprefixer'),
                    ],
                  },
                },
              },
              {
                loader: 'less-loader',
                options: {
                  sourceMap: shouldUseSourceMap,
                  lessOptions: {
                    javascriptEnabled: true,
                    // modifyVars: {
                    //   ...getLessVariables(paths.appCommonCss)
                    // }
                  },
                },
              },
              {
                loader: 'style-resources-loader',
                options: {
                  patterns: [paths.appCommonCss],
                },
              },
            ],
          },
          {
            test: /\.scss$/,
            sideEffects: true,
            use: [
              isDev ? 'style-loader' : MiniCssExtractPlugin.loader,
              {
                loader: 'css-loader',
                options: {
                  sourceMap: shouldUseSourceMap,
                  // 启用 css module
                  modules: {
                    auto: true,
                    localIdentName: '[local]--[hash:base64:5]',
                  },
                },
              },
              {
                loader: 'postcss-loader',
                options: {
                  sourceMap: shouldUseSourceMap,
                  postcssOptions: {
                    plugins: [
                      require('autoprefixer'),
                    ],
                  },
                },
              },
              {
                loader: 'sass-loader',
                options: {
                  sourceMap: shouldUseSourceMap,
                },
              },
            ],
            exclude: [paths.appCommon],
          },
        ],
      },
      {
        test: /\.mjs$/,
        include: /node_modules/,
        type: 'javascript/auto',
      },
    ],
  },
  plugins: [
    new HtmlWebpackPlugin(
      {
        env_my: process.env.APP_ENV,
        inject: true,
        template: paths.cooperAppHtml,
        chunks: ['cooper'],
        filename: 'index.html',
        ...(isProd
          ? {
            minify: { // 默认开启html-minifier-terser
              removeComments: true,
              collapseWhitespace: true,
              removeRedundantAttributes: true,
              useShortDoctype: true,
              removeEmptyAttributes: true,
              removeStyleLinkTypeAttributes: true,
              keepClosingSlash: true,
              minifyJS: true,
              minifyCSS: true,
              minifyURLs: true,
            },
            // inject: 'body', // js插入到body下面
          }
          : undefined),
      },
    ),
    new HtmlWebpackPlugin(
      {
        env_my: process.env.APP_ENV,
        inject: true,
        template: paths.knowledgeHtml,
        chunks: ['knowledge'],
        filename: 'knowledge.html',
        ...(isProd
          ? {
            minify: { // 默认开启html-minifier-terser
              removeComments: true,
              collapseWhitespace: true,
              removeRedundantAttributes: true,
              useShortDoctype: true,
              removeEmptyAttributes: true,
              removeStyleLinkTypeAttributes: true,
              keepClosingSlash: true,
              minifyJS: true,
              minifyCSS: true,
              minifyURLs: true,
            },
            // inject: 'body', // js插入到body下面
          }
          : undefined),
      },
    ),
    // new HtmlWebpackInjectPlugin(
    //   {
    //     dom: [{ reg: /id="root-skeleton">/, str: skeleton }],
    //   },
    // ),
    new CopyWebpackPlugin({
      patterns: [
        // 如果改成blob形式可以去掉
        {
          from: paths.codeWorker,
          to: paths.publicJs,
        },
      ],
    }),
    new webpack.DefinePlugin(definedEnv),
    new webpack.ProvidePlugin({
      React: 'react',
    }),
    // new HtmlWebpackExternalsPlugin({
    //   externals: [
    //     {
    //       module: 'react',
    //       entry: 'https://img-ys011.didistatic.com/static/cooper_cn/<EMAIL>',
    //       global: 'React',
    //     },
    //     {
    //       module: 'react-dom',
    //       entry: 'https://img-ys011.didistatic.com/static/cooper_cn/<EMAIL>',
    //       global: 'ReactDOM',
    //     },
    //     {
    //       module: 'react-redux',
    //       entry: 'https://img-ys011.didistatic.com/static/cooper_cn/<EMAIL>',
    //       global: 'ReactRedux',
    //     },
    //   ],
    // }),
    new ModuleNotFoundPlugin(paths.appPath),
    //  TODO:要不要开启esLint 配置
    // new ESLintPlugin({
    //   extensions: ['js', 'jsx'],
    //   formatter: require.resolve('react-dev-utils/eslintFormatter'),
    //   eslintPath: require.resolve('eslint'),
    //   context: paths.appSrc,
    //   cwd: paths.appPath,
    //   resolvePluginsRelativeTo: __dirname,
    // }),
  ],
  performance: false,
};
